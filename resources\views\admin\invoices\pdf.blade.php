<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><PERSON><PERSON><PERSON> đơn {{ $invoice->invoice_number }}</title>
    <style>
        body {
            font-family: Deja<PERSON>u Sans, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .invoice-title {
            font-size: 18px;
            font-weight: bold;
            margin-top: 20px;
        }
        .invoice-info {
            width: 100%;
            margin-bottom: 30px;
        }
        .invoice-info td {
            vertical-align: top;
            padding: 5px 0;
        }
        .customer-info {
            width: 50%;
        }
        .invoice-details {
            width: 50%;
            text-align: right;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .items-table .text-center {
            text-align: center;
        }
        .items-table .text-right {
            text-align: right;
        }
        .totals-table {
            width: 300px;
            margin-left: auto;
            border-collapse: collapse;
        }
        .totals-table td {
            padding: 5px 10px;
            border-bottom: 1px solid #ddd;
        }
        .totals-table .total-row {
            font-weight: bold;
            border-top: 2px solid #333;
            border-bottom: 2px solid #333;
        }
        .notes {
            margin-top: 30px;
        }
        .notes h4 {
            margin-bottom: 10px;
            font-size: 14px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }
        .status-sent {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .status-draft {
            background-color: #f8f9fa;
            color: #6c757d;
        }
        .status-overdue {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">CÔNG TY DỊCH VỤ SỐ</div>
        <div>Địa chỉ: 123 Đường ABC, Quận XYZ, TP. Hồ Chí Minh</div>
        <div>Điện thoại: 0123.456.789 | Email: <EMAIL></div>
        <div class="invoice-title">HÓA ĐƠN BÁN HÀNG</div>
    </div>

    <!-- Invoice Information -->
    <table class="invoice-info">
        <tr>
            <td class="customer-info">
                <h4>THÔNG TIN KHÁCH HÀNG:</h4>
                <strong>{{ $invoice->customer->name }}</strong><br>
                Mã khách hàng: {{ $invoice->customer->customer_code }}<br>
                @if($invoice->customer->email)
                    Email: {{ $invoice->customer->email }}<br>
                @endif
                @if($invoice->customer->phone)
                    Điện thoại: {{ $invoice->customer->phone }}<br>
                @endif
            </td>
            <td class="invoice-details">
                <h4>THÔNG TIN HÓA ĐƠN:</h4>
                <strong>Số hóa đơn:</strong> {{ $invoice->invoice_number }}<br>
                <strong>Ngày tạo:</strong> {{ $invoice->invoice_date->format('d/m/Y') }}<br>
                <strong>Hạn thanh toán:</strong> {{ $invoice->due_date->format('d/m/Y') }}<br>
                <strong>Trạng thái:</strong> 
                <span class="status-badge status-{{ $invoice->status }}">
                    @switch($invoice->status)
                        @case('draft') Nháp @break
                        @case('sent') Đã gửi @break
                        @case('paid') Đã thanh toán @break
                        @case('overdue') Quá hạn @break
                        @case('cancelled') Đã hủy @break
                    @endswitch
                </span><br>
                @if($invoice->status === 'paid' && $invoice->paid_at)
                    <strong>Ngày thanh toán:</strong> {{ $invoice->paid_at->format('d/m/Y') }}<br>
                @endif
            </td>
        </tr>
    </table>

    <!-- Invoice Items -->
    <table class="items-table">
        <thead>
            <tr>
                <th width="50%">Mô tả</th>
                <th width="15%" class="text-center">Số lượng</th>
                <th width="17.5%" class="text-right">Đơn giá</th>
                <th width="17.5%" class="text-right">Thành tiền</th>
            </tr>
        </thead>
        <tbody>
            @foreach($invoice->items as $item)
            <tr>
                <td>
                    {{ $item->description }}
                    @if($item->servicePackage)
                        <br><small>Gói: {{ $item->servicePackage->category->name }} - {{ $item->servicePackage->name }}</small>
                    @endif
                </td>
                <td class="text-center">{{ $item->quantity }}</td>
                <td class="text-right">{{ number_format($item->unit_price, 0, ',', '.') }} VNĐ</td>
                <td class="text-right">{{ number_format($item->total_price, 0, ',', '.') }} VNĐ</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <!-- Totals -->
    <table class="totals-table">
        <tr>
            <td>Tạm tính:</td>
            <td class="text-right">{{ number_format($invoice->subtotal, 0, ',', '.') }} VNĐ</td>
        </tr>
        @if($invoice->tax_amount > 0)
        <tr>
            <td>Thuế:</td>
            <td class="text-right">{{ number_format($invoice->tax_amount, 0, ',', '.') }} VNĐ</td>
        </tr>
        @endif
        @if($invoice->discount_amount > 0)
        <tr>
            <td>Giảm giá:</td>
            <td class="text-right">-{{ number_format($invoice->discount_amount, 0, ',', '.') }} VNĐ</td>
        </tr>
        @endif
        <tr class="total-row">
            <td><strong>Tổng cộng:</strong></td>
            <td class="text-right"><strong>{{ number_format($invoice->total_amount, 0, ',', '.') }} VNĐ</strong></td>
        </tr>
    </table>

    <!-- Notes and Payment Terms -->
    @if($invoice->notes || $invoice->payment_terms)
    <div class="notes">
        @if($invoice->notes)
        <div style="margin-bottom: 20px;">
            <h4>Ghi chú:</h4>
            <p>{{ $invoice->notes }}</p>
        </div>
        @endif
        
        @if($invoice->payment_terms)
        <div>
            <h4>Điều khoản thanh toán:</h4>
            <p>{{ $invoice->payment_terms }}</p>
        </div>
        @endif
    </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>Cảm ơn quý khách đã sử dụng dịch vụ của chúng tôi!</p>
        <p>Hóa đơn được tạo tự động vào {{ now()->format('d/m/Y H:i:s') }}</p>
    </div>
</body>
</html>
