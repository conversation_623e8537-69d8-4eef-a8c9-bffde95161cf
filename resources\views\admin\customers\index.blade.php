@extends('layouts.admin')

@section('title', 'Quản lý khách hàng')
@section('page-title', 'Quản lý khách hàng')

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0 fw-bold">
            <i class="fas fa-users me-2 text-primary"></i>
            Danh sách khách hàng
        </h5>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#quickAddModal">
                <i class="fas fa-plus me-1"></i>
                <span class="d-none d-sm-inline">Thêm nhanh</span>
            </button>
            <a href="{{ route('admin.customers.create', ['page' => request('page', 1), 'search' => request('search')]) }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                <span class="d-none d-sm-inline">Thêm khách hàng</span>
            </a>
        </div>
    </div>
    
    <div class="card-body">
        <!-- Search Form -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);">
                    <div class="card-body">
                        <form method="GET">
                            <div class="row align-items-end">
                                <div class="col-md-8 col-lg-9">
                                    <label class="form-label fw-semibold text-muted mb-2">
                                        <i class="fas fa-search me-1"></i>
                                        Tìm kiếm khách hàng
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-white border-end-0">
                                            <i class="fas fa-search text-muted"></i>
                                        </span>
                                        <input type="text"
                                               name="search"
                                               class="form-control border-start-0 ps-0"
                                               placeholder="Nhập tên, email, số điện thoại hoặc mã khách hàng..."
                                               value="{{ request('search') }}">
                                    </div>
                                </div>
                                <div class="col-md-4 col-lg-3">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary flex-fill">
                                            <i class="fas fa-search me-1"></i>
                                            <span class="d-none d-sm-inline">Tìm kiếm</span>
                                        </button>
                                        @if(request('search'))
                                            <a href="{{ route('admin.customers.index') }}" class="btn btn-outline-secondary" title="Xóa bộ lọc">
                                                <i class="fas fa-times"></i>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Results Info -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="d-flex align-items-center">
                <div class="badge bg-light text-dark border px-3 py-2 rounded-pill">
                    <i class="fas fa-info-circle me-1"></i>
                    Hiển thị {{ $customers->firstItem() ?? 0 }} - {{ $customers->lastItem() ?? 0 }}
                    trong tổng số <strong>{{ number_format($customers->total()) }}</strong> khách hàng
                </div>
            </div>
            @if($customers->total() > 0)
                <div class="text-muted small">
                    <i class="fas fa-clock me-1"></i>
                    Cập nhật lúc {{ now()->format('H:i') }}
                </div>
            @endif
        </div>
        
        <!-- Customers Table -->
        @if($customers->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th class="border-0 fw-bold text-uppercase">
                                <i class="fas fa-hashtag me-1 text-primary"></i>
                                Mã KH
                            </th>
                            <th class="border-0 fw-bold text-uppercase">
                                <i class="fas fa-user me-1 text-primary"></i>
                                Tên khách hàng
                            </th>
                            <th class="border-0 fw-bold text-uppercase d-none d-md-table-cell">
                                <i class="fas fa-envelope me-1 text-primary"></i>
                                Email
                            </th>
                            <th class="border-0 fw-bold text-uppercase d-none d-lg-table-cell">
                                <i class="fas fa-phone me-1 text-primary"></i>
                                Số điện thoại
                            </th>
                            <th class="border-0 fw-bold text-uppercase">
                                <i class="fas fa-box me-1 text-primary"></i>
                                Dịch vụ
                            </th>
                            <th class="border-0 fw-bold text-uppercase d-none d-xl-table-cell">
                                <i class="fas fa-calendar me-1 text-primary"></i>
                                Ngày tạo
                            </th>
                            <th class="border-0 fw-bold text-uppercase text-center">
                                <i class="fas fa-cogs me-1 text-primary"></i>
                                Thao tác
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($customers as $customer)
                            <tr class="customer-row">
                                <td>
                                    <span class="badge bg-primary fs-6 px-3 py-2">{{ $customer->customer_code }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            {{ strtoupper(substr($customer->name, 0, 1)) }}
                                        </div>
                                        <div>
                                            <div class="fw-bold text-dark">{{ $customer->name }}</div>
                                            <div class="d-md-none">
                                                @if($customer->email)
                                                    <small class="text-muted">{{ $customer->email }}</small>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell">
                                    @if($customer->email)
                                        <a href="mailto:{{ $customer->email }}" class="text-decoration-none text-primary">
                                            <i class="fas fa-envelope me-1"></i>
                                            {{ $customer->email }}
                                        </a>
                                    @else
                                        <span class="text-muted fst-italic">
                                            <i class="fas fa-minus me-1"></i>
                                            Chưa có email
                                        </span>
                                    @endif
                                </td>
                                <td class="d-none d-lg-table-cell">
                                    @if($customer->phone)
                                        <a href="tel:{{ $customer->phone }}" class="text-decoration-none text-success">
                                            <i class="fas fa-phone me-1"></i>
                                            {{ $customer->phone }}
                                        </a>
                                    @else
                                        <span class="text-muted fst-italic">
                                            <i class="fas fa-minus me-1"></i>
                                            Chưa có SĐT
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    @php
                                        $serviceCount = $customer->customerServices->count();
                                        $badgeClass = $serviceCount > 0 ? 'bg-success' : 'bg-secondary';
                                    @endphp
                                    <span class="badge {{ $badgeClass }} fs-6 px-3 py-2">
                                        <i class="fas fa-box me-1"></i>
                                        {{ $serviceCount }}
                                    </span>
                                </td>
                                <td class="d-none d-xl-table-cell">
                                    <div class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ $customer->created_at->format('d/m/Y') }}
                                    </div>
                                    <small class="text-muted">
                                        {{ $customer->created_at->format('H:i') }}
                                    </small>
                                </td>
                                <td>
                                    <div class="d-flex justify-content-center gap-1">
                                        <a href="{{ route('admin.customers.show', $customer) }}"
                                           class="btn btn-sm btn-outline-info rounded-pill"
                                           title="Xem chi tiết"
                                           data-bs-toggle="tooltip">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.customers.edit', $customer) }}"
                                           class="btn btn-sm btn-outline-warning rounded-pill"
                                           title="Chỉnh sửa"
                                           data-bs-toggle="tooltip">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ route('admin.customers.assign-service', $customer) }}"
                                           class="btn btn-sm btn-outline-success rounded-pill"
                                           title="Gán dịch vụ"
                                           data-bs-toggle="tooltip">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-danger rounded-pill"
                                                title="Xóa khách hàng"
                                                data-bs-toggle="tooltip"
                                                onclick="confirmDelete('{{ $customer->name }}', '{{ route('admin.customers.destroy', $customer) }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="text-muted">
                    Hiển thị {{ $customers->firstItem() ?? 0 }} đến {{ $customers->lastItem() ?? 0 }}
                    trong tổng số {{ $customers->total() }} khách hàng
                </div>
                <div>
                    {{ $customers->appends(request()->query())->links() }}
                </div>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Không tìm thấy khách hàng nào</h5>
                @if(request('search'))
                    <p class="text-muted">Thử thay đổi từ khóa tìm kiếm hoặc <a href="{{ route('admin.customers.index') }}">xóa bộ lọc</a></p>
                @else
                    <p class="text-muted">Hãy <a href="{{ route('admin.customers.create', ['page' => request('page', 1), 'search' => request('search')]) }}">thêm khách hàng đầu tiên</a></p>
                @endif
            </div>
        @endif
    </div>
</div>

<!-- Quick Add Customer Modal -->
<div class="modal fade" id="quickAddModal" tabindex="-1" aria-labelledby="quickAddModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="quickAddModalLabel">
                    <i class="fas fa-user-plus me-2"></i>
                    Thêm khách hàng nhanh
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="quickAddForm" method="POST" action="{{ route('admin.customers.store') }}">
                @csrf
                <input type="hidden" name="return_page" value="{{ request('page', 1) }}">
                <input type="hidden" name="return_search" value="{{ request('search') }}">

                <div class="modal-body">
                    <div class="mb-3">
                        <label for="quick_name" class="form-label fw-semibold">
                            Tên khách hàng <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="quick_name" name="name" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="quick_email" class="form-label fw-semibold">Email</label>
                            <input type="email" class="form-control" id="quick_email" name="email">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="quick_phone" class="form-label fw-semibold">Số điện thoại</label>
                            <input type="text" class="form-control" id="quick_phone" name="phone">
                        </div>
                    </div>
                    <div class="alert alert-info border-0">
                        <i class="fas fa-info-circle me-2"></i>
                        <small>Mã khách hàng sẽ được tự động tạo theo định dạng KUN#####</small>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        Hủy
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        Lưu khách hàng
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Xác nhận xóa
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-times fa-3x text-danger mb-3"></i>
                    <h6>Bạn có chắc chắn muốn xóa khách hàng:</h6>
                    <strong id="customerNameToDelete" class="text-primary"></strong>
                </div>
                <div class="alert alert-warning border-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <small>Hành động này không thể hoàn tác!</small>
                </div>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    Hủy
                </button>
                <form id="deleteForm" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>
                        Xóa khách hàng
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 16px;
        text-transform: uppercase;
    }

    .customer-row {
        transition: all 0.3s ease;
    }

    .customer-row:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
</style>

<script>
    // Initialize tooltips
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });

    // Confirm delete function
    function confirmDelete(customerName, deleteUrl) {
        document.getElementById('customerNameToDelete').textContent = customerName;
        document.getElementById('deleteForm').action = deleteUrl;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    // Quick add form validation
    document.getElementById('quickAddForm').addEventListener('submit', function(e) {
        const nameInput = document.getElementById('quick_name');
        if (!nameInput.value.trim()) {
            e.preventDefault();
            nameInput.focus();
            nameInput.classList.add('is-invalid');
        }
    });
</script>
@endpush
