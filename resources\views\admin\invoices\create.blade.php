@extends('layouts.admin')

@section('title', 'Tạo hóa đơn mới')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Tạo hóa đơn mới</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.invoices.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </div>

                <form method="POST" action="{{ route('admin.invoices.store') }}" id="invoice-form">
                    @csrf
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_id">Khách hàng <span class="text-danger">*</span></label>
                                    <select name="customer_id" id="customer_id" class="form-control @error('customer_id') is-invalid @enderror" required>
                                        <option value="">Chọn khách hàng</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}" {{ old('customer_id', $selectedCustomer?->id) == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }} ({{ $customer->customer_code }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('customer_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="invoice_date">Ngày hóa đơn <span class="text-danger">*</span></label>
                                    <input type="date" name="invoice_date" id="invoice_date" class="form-control @error('invoice_date') is-invalid @enderror" value="{{ old('invoice_date', date('Y-m-d')) }}" required>
                                    @error('invoice_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="due_date">Hạn thanh toán <span class="text-danger">*</span></label>
                                    <input type="date" name="due_date" id="due_date" class="form-control @error('due_date') is-invalid @enderror" value="{{ old('due_date', date('Y-m-d', strtotime('+30 days'))) }}" required>
                                    @error('due_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Invoice Items -->
                        <div class="row">
                            <div class="col-12">
                                <h5>Chi tiết hóa đơn</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="items-table">
                                        <thead>
                                            <tr>
                                                <th width="40%">Mô tả</th>
                                                <th width="15%">Số lượng</th>
                                                <th width="20%">Đơn giá</th>
                                                <th width="20%">Thành tiền</th>
                                                <th width="5%">Xóa</th>
                                            </tr>
                                        </thead>
                                        <tbody id="items-tbody">
                                            <tr class="item-row">
                                                <td>
                                                    <input type="text" name="items[0][description]" class="form-control" placeholder="Mô tả dịch vụ" required>
                                                    <select name="items[0][service_package_id]" class="form-control mt-1 service-package-select">
                                                        <option value="">Chọn gói dịch vụ (tùy chọn)</option>
                                                        @foreach($servicePackages as $package)
                                                            <option value="{{ $package->id }}" data-price="{{ $package->price }}">
                                                                {{ $package->category->name }} - {{ $package->name }} ({{ number_format($package->price, 0, ',', '.') }} VNĐ)
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" name="items[0][quantity]" class="form-control quantity-input" value="1" min="1" required>
                                                </td>
                                                <td>
                                                    <input type="number" name="items[0][unit_price]" class="form-control unit-price-input" step="0.01" min="0" required>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control total-price-display" readonly>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-danger btn-sm remove-item" disabled>
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <button type="button" class="btn btn-success" id="add-item">
                                    <i class="fas fa-plus"></i> Thêm dòng
                                </button>
                            </div>
                        </div>

                        <!-- Totals -->
                        <div class="row mt-4">
                            <div class="col-md-8"></div>
                            <div class="col-md-4">
                                <table class="table">
                                    <tr>
                                        <td><strong>Tạm tính:</strong></td>
                                        <td class="text-right"><span id="subtotal">0</span> VNĐ</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <label for="tax_amount">Thuế:</label>
                                            <input type="number" name="tax_amount" id="tax_amount" class="form-control" step="0.01" min="0" value="{{ old('tax_amount', 0) }}">
                                        </td>
                                        <td class="text-right"><span id="tax-display">0</span> VNĐ</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <label for="discount_amount">Giảm giá:</label>
                                            <input type="number" name="discount_amount" id="discount_amount" class="form-control" step="0.01" min="0" value="{{ old('discount_amount', 0) }}">
                                        </td>
                                        <td class="text-right"><span id="discount-display">0</span> VNĐ</td>
                                    </tr>
                                    <tr class="table-active">
                                        <td><strong>Tổng cộng:</strong></td>
                                        <td class="text-right"><strong><span id="total">0</span> VNĐ</strong></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="notes">Ghi chú</label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3">{{ old('notes') }}</textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="payment_terms">Điều khoản thanh toán</label>
                                    <textarea name="payment_terms" id="payment_terms" class="form-control" rows="3">{{ old('payment_terms', 'Thanh toán trong vòng 30 ngày kể từ ngày xuất hóa đơn.') }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Tạo hóa đơn
                        </button>
                        <a href="{{ route('admin.invoices.index') }}" class="btn btn-secondary">Hủy</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let itemIndex = 1;

// Add new item row
$('#add-item').click(function() {
    const newRow = `
        <tr class="item-row">
            <td>
                <input type="text" name="items[${itemIndex}][description]" class="form-control" placeholder="Mô tả dịch vụ" required>
                <select name="items[${itemIndex}][service_package_id]" class="form-control mt-1 service-package-select">
                    <option value="">Chọn gói dịch vụ (tùy chọn)</option>
                    @foreach($servicePackages as $package)
                        <option value="{{ $package->id }}" data-price="{{ $package->price }}">
                            {{ $package->category->name }} - {{ $package->name }} ({{ number_format($package->price, 0, ',', '.') }} VNĐ)
                        </option>
                    @endforeach
                </select>
            </td>
            <td>
                <input type="number" name="items[${itemIndex}][quantity]" class="form-control quantity-input" value="1" min="1" required>
            </td>
            <td>
                <input type="number" name="items[${itemIndex}][unit_price]" class="form-control unit-price-input" step="0.01" min="0" required>
            </td>
            <td>
                <input type="number" class="form-control total-price-display" readonly>
            </td>
            <td>
                <button type="button" class="btn btn-danger btn-sm remove-item">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
    $('#items-tbody').append(newRow);
    itemIndex++;
    updateRemoveButtons();
});

// Remove item row
$(document).on('click', '.remove-item', function() {
    $(this).closest('tr').remove();
    updateRemoveButtons();
    calculateTotals();
});

// Update remove buttons
function updateRemoveButtons() {
    const rows = $('.item-row').length;
    $('.remove-item').prop('disabled', rows <= 1);
}

// Service package selection
$(document).on('change', '.service-package-select', function() {
    const price = $(this).find(':selected').data('price');
    if (price) {
        $(this).closest('tr').find('.unit-price-input').val(price);
        calculateRowTotal($(this).closest('tr'));
    }
});

// Calculate row total
function calculateRowTotal(row) {
    const quantity = parseFloat(row.find('.quantity-input').val()) || 0;
    const unitPrice = parseFloat(row.find('.unit-price-input').val()) || 0;
    const total = quantity * unitPrice;
    row.find('.total-price-display').val(total.toFixed(2));
    calculateTotals();
}

// Calculate all totals
function calculateTotals() {
    let subtotal = 0;
    $('.item-row').each(function() {
        const total = parseFloat($(this).find('.total-price-display').val()) || 0;
        subtotal += total;
    });
    
    const tax = parseFloat($('#tax_amount').val()) || 0;
    const discount = parseFloat($('#discount_amount').val()) || 0;
    const total = subtotal + tax - discount;
    
    $('#subtotal').text(subtotal.toLocaleString('vi-VN'));
    $('#tax-display').text(tax.toLocaleString('vi-VN'));
    $('#discount-display').text(discount.toLocaleString('vi-VN'));
    $('#total').text(total.toLocaleString('vi-VN'));
}

// Event listeners
$(document).on('input', '.quantity-input, .unit-price-input', function() {
    calculateRowTotal($(this).closest('tr'));
});

$(document).on('input', '#tax_amount, #discount_amount', function() {
    calculateTotals();
});

// Initialize
$(document).ready(function() {
    updateRemoveButtons();
    calculateTotals();
});
</script>
@endpush
@endsection
