@extends('layouts.admin')

@section('title', '<PERSON>ị<PERSON> đăng bài')
@section('page-title', 'Lịch đăng bài')

@section('content')
<div class="row mb-4">
    <div class="col-md-6">
        <a href="{{ route('admin.content-scheduler.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Tạo bài đăng mới
        </a>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary" id="calendarView">
                <i class="fas fa-calendar me-1"></i>Lịch
            </button>
            <button type="button" class="btn btn-outline-secondary active" id="listView">
                <i class="fas fa-list me-1"></i>Danh sách
            </button>
        </div>
    </div>
</div>

<!-- Calendar View -->
<div class="card" id="calendar-container" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-calendar-alt me-2"></i>Lịch đăng bài
        </h5>
        <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-outline-primary" id="todayBtn">
                <i class="fas fa-calendar-day me-1"></i>Hôm nay
            </button>
            <button type="button" class="btn btn-outline-success" id="addEventBtn">
                <i class="fas fa-plus me-1"></i>Thêm bài đăng
            </button>
        </div>
    </div>
    <div class="card-body">
        <div id="calendar"></div>
    </div>
</div>

<!-- List View -->
<div id="list-container">
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Tìm kiếm</label>
                    <input type="text" class="form-control" name="search" value="{{ request('search') }}" placeholder="Tiêu đề, nội dung...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Trạng thái</label>
                    <select class="form-select" name="status">
                        <option value="">Tất cả</option>
                        <option value="scheduled" {{ request('status') == 'scheduled' ? 'selected' : '' }}>Đã lên lịch</option>
                        <option value="posted" {{ request('status') == 'posted' ? 'selected' : '' }}>Đã đăng</option>
                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Đã hủy</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Từ ngày</label>
                    <input type="date" class="form-control" name="date_from" value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Đến ngày</label>
                    <input type="date" class="form-control" name="date_to" value="{{ request('date_to') }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Lọc
                        </button>
                        <a href="{{ route('admin.content-scheduler.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Xóa
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Content Posts List -->
    <div class="card">
        <div class="card-body">
            @if($contentPosts->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Tiêu đề</th>
                                <th>Nhóm đích</th>
                                <th>Thời gian đăng</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($contentPosts as $post)
                                <tr>
                                    <td>
                                        <div class="fw-bold">{{ $post->title }}</div>
                                        <small class="text-muted">{{ Str::limit($post->content, 50) }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $post->target_groups_string }}</span>
                                    </td>
                                    <td>
                                        <div>{{ $post->scheduled_at->format('d/m/Y H:i') }}</div>
                                        @if($post->isOverdue())
                                            <small class="text-danger">
                                                <i class="fas fa-exclamation-triangle me-1"></i>Quá hạn
                                            </small>
                                        @elseif($post->needsReminder())
                                            <small class="text-warning">
                                                <i class="fas fa-clock me-1"></i>Sắp đến giờ
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($post->status == 'scheduled')
                                            <span class="badge bg-primary">Đã lên lịch</span>
                                        @elseif($post->status == 'posted')
                                            <span class="badge bg-success">Đã đăng</span>
                                        @else
                                            <span class="badge bg-danger">Đã hủy</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('admin.content-scheduler.show', $post) }}" class="btn btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.content-scheduler.edit', $post) }}" class="btn btn-outline-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if($post->status == 'scheduled')
                                                <form method="POST" action="{{ route('admin.content-scheduler.mark-posted', $post) }}" class="d-inline">
                                                    @csrf
                                                    @method('PATCH')
                                                    <button type="submit" class="btn btn-outline-success" onclick="return confirm('Đánh dấu bài đăng này là đã đăng?')">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            @endif
                                            <form method="POST" action="{{ route('admin.content-scheduler.destroy', $post) }}" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-outline-danger" onclick="return confirm('Bạn có chắc muốn xóa bài đăng này?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $contentPosts->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Chưa có bài đăng nào</h5>
                    <p class="text-muted">Hãy tạo bài đăng đầu tiên của bạn!</p>
                    <a href="{{ route('admin.content-scheduler.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Tạo bài đăng mới
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- FullCalendar CSS & JS -->
<link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css' rel='stylesheet' />
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calendarEl = document.getElementById('calendar');
    const calendarContainer = document.getElementById('calendar-container');
    const listContainer = document.getElementById('list-container');
    const calendarViewBtn = document.getElementById('calendarView');
    const listViewBtn = document.getElementById('listView');

    // Initialize FullCalendar
    const calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
        },
        height: 'auto',
        locale: 'vi',
        firstDay: 1, // Monday
        events: '{{ route("admin.content-scheduler.calendar") }}',
        eventClick: function(info) {
            showEventModal(info.event);
        },
        eventDidMount: function(info) {
            // Add tooltip with content preview
            const content = info.event.extendedProps.content;
            const truncatedContent = content.length > 100 ? content.substring(0, 100) + '...' : content;
            info.el.setAttribute('title', truncatedContent);

            // Add status indicator
            const status = info.event.extendedProps.status;
            if (status === 'posted') {
                info.el.style.opacity = '0.7';
                info.el.style.textDecoration = 'line-through';
            }
        },
        dateClick: function(info) {
            // Quick add event on date click
            const selectedDate = info.dateStr;
            window.location.href = `{{ route('admin.content-scheduler.create') }}?date=${selectedDate}`;
        },
        eventDrop: function(info) {
            // Handle drag and drop (if we implement it later)
            updateEventDate(info.event.id, info.event.start);
        }
    });

    // View toggle functionality
    calendarViewBtn.addEventListener('click', function() {
        calendarContainer.style.display = 'block';
        listContainer.style.display = 'none';
        calendarViewBtn.classList.add('active');
        listViewBtn.classList.remove('active');
        calendar.render();
    });

    listViewBtn.addEventListener('click', function() {
        calendarContainer.style.display = 'none';
        listContainer.style.display = 'block';
        listViewBtn.classList.add('active');
        calendarViewBtn.classList.remove('active');
    });

    // Today button functionality
    document.getElementById('todayBtn').addEventListener('click', function() {
        calendar.today();
    });

    // Add event button functionality
    document.getElementById('addEventBtn').addEventListener('click', function() {
        window.location.href = '{{ route("admin.content-scheduler.create") }}';
    });

    // Show event details modal
    function showEventModal(event) {
        const modal = document.getElementById('eventModal');
        if (!modal) {
            createEventModal();
        }

        document.getElementById('modalTitle').textContent = event.title;
        document.getElementById('modalContent').textContent = event.extendedProps.content;
        document.getElementById('modalTargetGroups').textContent = event.extendedProps.target_groups;
        document.getElementById('modalStatus').textContent = getStatusText(event.extendedProps.status);
        document.getElementById('modalStatus').className = `badge bg-${getStatusColor(event.extendedProps.status)}`;
        document.getElementById('modalScheduledAt').textContent = event.start.toLocaleString('vi-VN');

        // Set action buttons
        const editUrl = '{{ route("admin.content-scheduler.edit", ":id") }}'.replace(':id', event.id);
        const viewUrl = '{{ route("admin.content-scheduler.show", ":id") }}'.replace(':id', event.id);
        document.getElementById('editBtn').href = editUrl;
        document.getElementById('viewBtn').href = viewUrl;

        const markPostedBtn = document.getElementById('markPostedBtn');
        if (event.extendedProps.status === 'scheduled') {
            markPostedBtn.style.display = 'inline-block';
            markPostedBtn.onclick = function() {
                markAsPosted(event.id);
            };
        } else {
            markPostedBtn.style.display = 'none';
        }

        new bootstrap.Modal(modal).show();
    }

    // Create event modal if it doesn't exist
    function createEventModal() {
        const modalHtml = `
            <div class="modal fade" id="eventModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="modalTitle"></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <strong>Nội dung:</strong>
                                <p id="modalContent" class="mt-1"></p>
                            </div>
                            <div class="mb-3">
                                <strong>Nhóm đích:</strong>
                                <span id="modalTargetGroups" class="badge bg-info ms-2"></span>
                            </div>
                            <div class="mb-3">
                                <strong>Trạng thái:</strong>
                                <span id="modalStatus" class="badge ms-2"></span>
                            </div>
                            <div class="mb-3">
                                <strong>Thời gian đăng:</strong>
                                <span id="modalScheduledAt"></span>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <a href="#" id="viewBtn" class="btn btn-info">
                                <i class="fas fa-eye me-1"></i>Xem chi tiết
                            </a>
                            <a href="#" id="editBtn" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>Chỉnh sửa
                            </a>
                            <button type="button" id="markPostedBtn" class="btn btn-success">
                                <i class="fas fa-check me-1"></i>Đánh dấu đã đăng
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    // Helper functions
    function getStatusText(status) {
        switch(status) {
            case 'scheduled': return 'Đã lên lịch';
            case 'posted': return 'Đã đăng';
            case 'cancelled': return 'Đã hủy';
            default: return status;
        }
    }

    function getStatusColor(status) {
        switch(status) {
            case 'scheduled': return 'primary';
            case 'posted': return 'success';
            case 'cancelled': return 'danger';
            default: return 'secondary';
        }
    }

    function markAsPosted(eventId) {
        if (confirm('Đánh dấu bài đăng này là đã đăng?')) {
            const markPostedUrl = '{{ route("admin.content-scheduler.mark-posted", ":id") }}'.replace(':id', eventId);
            fetch(markPostedUrl, {
                method: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    calendar.refetchEvents();
                    bootstrap.Modal.getInstance(document.getElementById('eventModal')).hide();
                    location.reload(); // Refresh to show success message
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra khi cập nhật trạng thái');
            });
        }
    }

    function updateEventDate(eventId, newDate) {
        // This would be used for drag & drop functionality
        // Implementation depends on your backend API
        console.log('Update event', eventId, 'to', newDate);
    }
});
</script>
@endpush
