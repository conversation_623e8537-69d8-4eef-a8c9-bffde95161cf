@extends('layouts.admin')

@section('title', 'Chi tiết hóa đơn')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Hóa đơn {{ $invoice->invoice_number }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.invoices.edit', $invoice) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Sửa
                        </a>
                        <a href="{{ route('admin.invoices.pdf', $invoice) }}" class="btn btn-secondary" target="_blank">
                            <i class="fas fa-file-pdf"></i> Xuất PDF
                        </a>
                        @if($invoice->status !== 'paid')
                            <form method="POST" action="{{ route('admin.invoices.mark-paid', $invoice) }}" class="d-inline">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="btn btn-success" onclick="return confirm('Đánh dấu hóa đơn này là đã thanh toán?')">
                                    <i class="fas fa-check"></i> Đánh dấu đã thanh toán
                                </button>
                            </form>
                        @endif
                        <a href="{{ route('admin.invoices.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Invoice Header -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Thông tin khách hàng</h5>
                            <address>
                                <strong>{{ $invoice->customer->name }}</strong><br>
                                Mã khách hàng: {{ $invoice->customer->customer_code }}<br>
                                @if($invoice->customer->email)
                                    Email: {{ $invoice->customer->email }}<br>
                                @endif
                                @if($invoice->customer->phone)
                                    Điện thoại: {{ $invoice->customer->phone }}<br>
                                @endif
                            </address>
                        </div>
                        <div class="col-md-6 text-right">
                            <h5>Thông tin hóa đơn</h5>
                            <p>
                                <strong>Số hóa đơn:</strong> {{ $invoice->invoice_number }}<br>
                                <strong>Ngày tạo:</strong> {{ $invoice->invoice_date->format('d/m/Y') }}<br>
                                <strong>Hạn thanh toán:</strong> {{ $invoice->due_date->format('d/m/Y') }}<br>
                                <strong>Trạng thái:</strong> 
                                @switch($invoice->status)
                                    @case('draft')
                                        <span class="badge badge-secondary">Nháp</span>
                                        @break
                                    @case('sent')
                                        <span class="badge badge-info">Đã gửi</span>
                                        @break
                                    @case('paid')
                                        <span class="badge badge-success">Đã thanh toán</span>
                                        @if($invoice->paid_at)
                                            <br><small>Thanh toán lúc: {{ $invoice->paid_at->format('d/m/Y H:i') }}</small>
                                        @endif
                                        @break
                                    @case('overdue')
                                        <span class="badge badge-danger">Quá hạn</span>
                                        @break
                                    @case('cancelled')
                                        <span class="badge badge-dark">Đã hủy</span>
                                        @break
                                @endswitch
                                @if($invoice->isOverdue() && $invoice->status !== 'paid')
                                    <br><span class="badge badge-danger">Quá hạn {{ $invoice->due_date->diffInDays(now()) }} ngày</span>
                                @endif
                            </p>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="thead-light">
                                <tr>
                                    <th>Mô tả</th>
                                    <th width="10%">Số lượng</th>
                                    <th width="15%">Đơn giá</th>
                                    <th width="15%">Thành tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($invoice->items as $item)
                                <tr>
                                    <td>
                                        {{ $item->description }}
                                        @if($item->servicePackage)
                                            <br><small class="text-muted">Gói: {{ $item->servicePackage->category->name }} - {{ $item->servicePackage->name }}</small>
                                        @endif
                                    </td>
                                    <td class="text-center">{{ $item->quantity }}</td>
                                    <td class="text-right">{{ number_format($item->unit_price, 0, ',', '.') }} VNĐ</td>
                                    <td class="text-right">{{ number_format($item->total_price, 0, ',', '.') }} VNĐ</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Invoice Totals -->
                    <div class="row">
                        <div class="col-md-8"></div>
                        <div class="col-md-4">
                            <table class="table">
                                <tr>
                                    <td><strong>Tạm tính:</strong></td>
                                    <td class="text-right">{{ number_format($invoice->subtotal, 0, ',', '.') }} VNĐ</td>
                                </tr>
                                @if($invoice->tax_amount > 0)
                                <tr>
                                    <td>Thuế:</td>
                                    <td class="text-right">{{ number_format($invoice->tax_amount, 0, ',', '.') }} VNĐ</td>
                                </tr>
                                @endif
                                @if($invoice->discount_amount > 0)
                                <tr>
                                    <td>Giảm giá:</td>
                                    <td class="text-right">-{{ number_format($invoice->discount_amount, 0, ',', '.') }} VNĐ</td>
                                </tr>
                                @endif
                                <tr class="table-active">
                                    <td><strong>Tổng cộng:</strong></td>
                                    <td class="text-right"><strong>{{ number_format($invoice->total_amount, 0, ',', '.') }} VNĐ</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Notes and Payment Terms -->
                    @if($invoice->notes || $invoice->payment_terms)
                    <div class="row mt-4">
                        @if($invoice->notes)
                        <div class="col-md-6">
                            <h6>Ghi chú:</h6>
                            <p>{{ $invoice->notes }}</p>
                        </div>
                        @endif
                        @if($invoice->payment_terms)
                        <div class="col-md-6">
                            <h6>Điều khoản thanh toán:</h6>
                            <p>{{ $invoice->payment_terms }}</p>
                        </div>
                        @endif
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
