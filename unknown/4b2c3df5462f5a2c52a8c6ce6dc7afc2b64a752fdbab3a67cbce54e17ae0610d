@extends('layouts.admin')

@section('title', 'Quản lý hóa đơn')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Danh sách hóa đơn</h3>
                    <a href="{{ route('admin.invoices.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Tạo hóa đơn mới
                    </a>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <select name="status" class="form-control">
                                    <option value="">Tất cả trạng thái</option>
                                    <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Nháp</option>
                                    <option value="sent" {{ request('status') == 'sent' ? 'selected' : '' }}>Đ<PERSON> gửi</option>
                                    <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>Đã thanh toán</option>
                                    <option value="overdue" {{ request('status') == 'overdue' ? 'selected' : '' }}>Quá hạn</option>
                                    <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Đã hủy</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}" placeholder="Từ ngày">
                            </div>
                            <div class="col-md-2">
                                <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}" placeholder="Đến ngày">
                            </div>
                            <div class="col-md-3">
                                <input type="text" name="search" class="form-control" value="{{ request('search') }}" placeholder="Tìm kiếm...">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-secondary">Lọc</button>
                                <a href="{{ route('admin.invoices.index') }}" class="btn btn-outline-secondary">Reset</a>
                            </div>
                        </div>
                    </form>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Số hóa đơn</th>
                                    <th>Khách hàng</th>
                                    <th>Ngày tạo</th>
                                    <th>Hạn thanh toán</th>
                                    <th>Tổng tiền</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($invoices as $invoice)
                                <tr>
                                    <td>
                                        <a href="{{ route('admin.invoices.show', $invoice) }}" class="text-decoration-none">
                                            {{ $invoice->invoice_number }}
                                        </a>
                                    </td>
                                    <td>
                                        <strong>{{ $invoice->customer->name }}</strong><br>
                                        <small class="text-muted">{{ $invoice->customer->customer_code }}</small>
                                    </td>
                                    <td>{{ $invoice->invoice_date->format('d/m/Y') }}</td>
                                    <td>
                                        {{ $invoice->due_date->format('d/m/Y') }}
                                        @if($invoice->isOverdue())
                                            <span class="badge badge-danger ml-1">Quá hạn</span>
                                        @endif
                                    </td>
                                    <td>{{ number_format($invoice->total_amount, 0, ',', '.') }} VNĐ</td>
                                    <td>
                                        @switch($invoice->status)
                                            @case('draft')
                                                <span class="badge badge-secondary">Nháp</span>
                                                @break
                                            @case('sent')
                                                <span class="badge badge-info">Đã gửi</span>
                                                @break
                                            @case('paid')
                                                <span class="badge badge-success">Đã thanh toán</span>
                                                @break
                                            @case('overdue')
                                                <span class="badge badge-danger">Quá hạn</span>
                                                @break
                                            @case('cancelled')
                                                <span class="badge badge-dark">Đã hủy</span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.invoices.show', $invoice) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.invoices.edit', $invoice) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ route('admin.invoices.pdf', $invoice) }}" class="btn btn-sm btn-secondary" target="_blank">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                            @if($invoice->status !== 'paid')
                                                <form method="POST" action="{{ route('admin.invoices.mark-paid', $invoice) }}" class="d-inline">
                                                    @csrf
                                                    @method('PATCH')
                                                    <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Đánh dấu hóa đơn này là đã thanh toán?')">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center">Không có hóa đơn nào</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {{ $invoices->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
