<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Lead extends Model
{
    protected $fillable = [
        'name',
        'phone',
        'zalo',
        'email',
        'needs',
        'status',
        'notes',
        'source',
        'potential_value',
    ];

    protected $casts = [
        'potential_value' => 'decimal:2',
    ];

    /**
     * Relationship with lead care schedules
     */
    public function careSchedules(): HasMany
    {
        return $this->hasMany(LeadCareSchedule::class);
    }

    /**
     * Get upcoming care schedules
     */
    public function upcomingCareSchedules(): HasMany
    {
        return $this->hasMany(LeadCareSchedule::class)
            ->where('scheduled_at', '>', now())
            ->where('status', 'scheduled')
            ->orderBy('scheduled_at');
    }

    /**
     * Get overdue care schedules
     */
    public function overdueCareSchedules(): HasMany
    {
        return $this->hasMany(LeadCareSchedule::class)
            ->where('scheduled_at', '<', now())
            ->where('status', 'scheduled')
            ->orderBy('scheduled_at');
    }

    /**
     * Scope for filtering by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for new leads
     */
    public function scopeNew($query)
    {
        return $query->where('status', 'new');
    }

    /**
     * Scope for interested leads
     */
    public function scopeInterested($query)
    {
        return $query->where('status', 'interested');
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'new' => 'Mới',
            'contacted' => 'Đã liên hệ',
            'interested' => 'Quan tâm',
            'not_interested' => 'Không quan tâm',
            'converted' => 'Đã chuyển đổi',
            default => 'Không xác định',
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'new' => 'primary',
            'contacted' => 'info',
            'interested' => 'warning',
            'not_interested' => 'danger',
            'converted' => 'success',
            default => 'secondary',
        };
    }

    /**
     * Check if lead needs care today
     */
    public function needsCareToday(): bool
    {
        return $this->careSchedules()
            ->whereDate('scheduled_at', today())
            ->where('status', 'scheduled')
            ->exists();
    }

    /**
     * Check if lead has overdue care
     */
    public function hasOverdueCare(): bool
    {
        return $this->overdueCareSchedules()->exists();
    }
}
