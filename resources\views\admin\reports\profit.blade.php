@extends('layouts.admin')

@section('title', 'Báo cáo lợi nhuận')
@section('page-title', 'Báo cáo lợi nhuận')

@section('content')
<!-- <PERSON><PERSON> lọc thời gian -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row align-items-end">
            <div class="col-md-3">
                <label for="start_date" class="form-label">Từ ngày</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $startDate }}">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">Đến ngày</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $endDate }}">
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>
                    Xem báo cáo
                </button>
            </div>
            <div class="col-md-3 text-end">
                <small class="text-muted">
                    Từ {{ \Carbon\Carbon::parse($startDate)->format('d/m/Y') }} 
                    đến {{ \Carbon\Carbon::parse($endDate)->format('d/m/Y') }}
                </small>
            </div>
        </form>
    </div>
</div>

<!-- Tổng quan -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Dịch vụ đã bán</h6>
                        <h3 class="mb-0">{{ $soldServices->count() }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Tổng doanh thu</h6>
                        <h3 class="mb-0">{{ number_format($totalRevenue) }}đ</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Tổng chi phí</h6>
                        <h3 class="mb-0">{{ number_format($totalCost) }}đ</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-credit-card fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Lợi nhuận</h6>
                        <h3 class="mb-0">{{ number_format($totalProfit) }}đ</h3>
                        @if($totalCost > 0)
                            <small>{{ round(($totalProfit / $totalCost) * 100, 2) }}%</small>
                        @endif
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Thống kê theo gói dịch vụ -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    Lợi nhuận theo gói dịch vụ
                </h5>
            </div>
            <div class="card-body">
                @if($packageStats->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Gói dịch vụ</th>
                                    <th>Số lượng bán</th>
                                    <th>Doanh thu</th>
                                    <th>Chi phí</th>
                                    <th>Lợi nhuận</th>
                                    <th>Tỷ lệ LN</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($packageStats as $stat)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $stat['package']->name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $stat['package']->category->name }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $stat['count'] }}</span>
                                        </td>
                                        <td>
                                            <strong class="text-success">{{ number_format($stat['revenue']) }}đ</strong>
                                        </td>
                                        <td>
                                            <span class="text-danger">{{ number_format($stat['cost']) }}đ</span>
                                        </td>
                                        <td>
                                            <strong class="text-info">{{ number_format($stat['profit']) }}đ</strong>
                                        </td>
                                        <td>
                                            @if($stat['profit_margin'] > 0)
                                                <span class="badge bg-success">{{ $stat['profit_margin'] }}%</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">Không có dữ liệu trong khoảng thời gian này</h6>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Thống kê theo tháng -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar me-2"></i>
                    Thống kê 6 tháng gần nhất
                </h5>
            </div>
            <div class="card-body">
                @foreach($monthlyStats as $month)
                    <div class="d-flex justify-content-between align-items-center mb-3 p-2 bg-light rounded">
                        <div>
                            <strong>{{ $month['month'] }}</strong>
                            <br>
                            <small class="text-muted">{{ $month['count'] }} dịch vụ</small>
                        </div>
                        <div class="text-end">
                            <div class="text-success">{{ number_format($month['revenue']) }}đ</div>
                            <div class="text-info">+{{ number_format($month['profit']) }}đ</div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>

<!-- Chi tiết dịch vụ đã bán -->
@if($soldServices->count() > 0)
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Chi tiết dịch vụ đã bán ({{ $soldServices->count() }})
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Ngày bán</th>
                        <th>Khách hàng</th>
                        <th>Gói dịch vụ</th>
                        <th>Giá bán</th>
                        <th>Giá nhập</th>
                        <th>Lợi nhuận</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($soldServices as $service)
                        <tr>
                            <td>{{ $service->activated_at->format('d/m/Y') }}</td>
                            <td>
                                <div>
                                    {{ $service->customer->name }}
                                    <br>
                                    <small class="text-muted">{{ $service->customer->customer_code }}</small>
                                </div>
                            </td>
                            <td>{{ $service->servicePackage->name }}</td>
                            <td class="text-success">{{ number_format($service->servicePackage->price) }}đ</td>
                            <td class="text-danger">
                                {{ $service->servicePackage->cost_price ? number_format($service->servicePackage->cost_price) . 'đ' : '-' }}
                            </td>
                            <td class="text-info">
                                @if($service->servicePackage->cost_price)
                                    {{ number_format($service->servicePackage->getProfit()) }}đ
                                @else
                                    -
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@endif
@endsection
