<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerService extends Model
{
    protected $fillable = [
        'customer_id',
        'service_package_id',
        'login_email',
        'login_password',
        'activated_at',
        'expires_at',
        'status',
        'internal_notes',
    ];

    protected $casts = [
        'activated_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function servicePackage(): BelongsTo
    {
        return $this->belongsTo(ServicePackage::class);
    }

    // Kiểm tra xem dịch vụ có sắp hết hạn không (trong vòng 5 ngày)
    public function isExpiringSoon(): bool
    {
        if (!$this->expires_at || $this->expires_at->isPast()) {
            return false;
        }

        $daysRemaining = $this->getDaysRemaining();
        return $daysRemaining > 0 && $daysRemaining <= 5;
    }

    // L<PERSON>y số ngày còn lại
    public function getDaysRemaining(): int
    {
        if (!$this->expires_at) {
            return 0;
        }

        if ($this->expires_at->isPast()) {
            return 0;
        }

        // Tính số ngày còn lại từ hôm nay đến ngày hết hạn
        return (int) now()->diffInDays($this->expires_at, false);
    }

    // Kiểm tra xem dịch vụ đã hết hạn chưa
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    // Scope để lấy các dịch vụ đang hoạt động
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Scope để lấy các dịch vụ sắp hết hạn (mặc định 5 ngày)
    public function scopeExpiringSoon($query, $days = 5)
    {
        return $query->where('status', 'active')
            ->where('expires_at', '>', now())
            ->where('expires_at', '<=', now()->addDays($days));
    }

    // Scope để lấy các dịch vụ đã hết hạn
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }
}
